import { useEffect, useRef } from 'react'
import * as THREE from 'three'

const ThreeBackground = () => {
  const mountRef = useRef(null)
  const sceneRef = useRef(null)
  const rendererRef = useRef(null)
  const animationIdRef = useRef(null)

  useEffect(() => {
    if (!mountRef.current) return

    // Scene setup
    const scene = new THREE.Scene()
    scene.background = new THREE.Color(0x000000) // Pure black background
    sceneRef.current = scene

    // Camera setup
    const camera = new THREE.PerspectiveCamera(
      75,
      window.innerWidth / window.innerHeight,
      0.1,
      1000
    )
    camera.position.z = 5

    // Renderer setup
    const renderer = new THREE.WebGLRenderer({ 
      antialias: true,
      alpha: true 
    })
    renderer.setSize(window.innerWidth, window.innerHeight)
    renderer.setPixelRatio(window.devicePixelRatio)
    rendererRef.current = renderer
    mountRef.current.appendChild(renderer.domElement)

    // Create floating particles
    const particleGeometry = new THREE.BufferGeometry()
    const particleCount = 1000
    const positions = new Float32Array(particleCount * 3)
    const colors = new Float32Array(particleCount * 3)

    for (let i = 0; i < particleCount * 3; i += 3) {
      // Random positions
      positions[i] = (Math.random() - 0.5) * 20
      positions[i + 1] = (Math.random() - 0.5) * 20
      positions[i + 2] = (Math.random() - 0.5) * 20

      // Color gradient from blue to purple to pink
      const colorChoice = Math.random()
      if (colorChoice < 0.33) {
        // Blue
        colors[i] = 0.2
        colors[i + 1] = 0.4
        colors[i + 2] = 1.0
      } else if (colorChoice < 0.66) {
        // Purple
        colors[i] = 0.6
        colors[i + 1] = 0.2
        colors[i + 2] = 1.0
      } else {
        // Pink
        colors[i] = 1.0
        colors[i + 1] = 0.2
        colors[i + 2] = 0.6
      }
    }

    particleGeometry.setAttribute('position', new THREE.BufferAttribute(positions, 3))
    particleGeometry.setAttribute('color', new THREE.BufferAttribute(colors, 3))

    const particleMaterial = new THREE.PointsMaterial({
      size: 0.02,
      vertexColors: true,
      transparent: true,
      opacity: 0.8,
      blending: THREE.AdditiveBlending
    })

    const particles = new THREE.Points(particleGeometry, particleMaterial)
    scene.add(particles)

    // Create geometric shapes
    const shapes = []
    
    // Wireframe spheres
    for (let i = 0; i < 5; i++) {
      const geometry = new THREE.SphereGeometry(0.5, 16, 16)
      const material = new THREE.MeshBasicMaterial({
        color: new THREE.Color().setHSL(0.6 + i * 0.1, 0.8, 0.5),
        wireframe: true,
        transparent: true,
        opacity: 0.3
      })
      const sphere = new THREE.Mesh(geometry, material)
      sphere.position.set(
        (Math.random() - 0.5) * 10,
        (Math.random() - 0.5) * 10,
        (Math.random() - 0.5) * 10
      )
      shapes.push(sphere)
      scene.add(sphere)
    }

    // Wireframe torus
    for (let i = 0; i < 3; i++) {
      const geometry = new THREE.TorusGeometry(1, 0.3, 8, 16)
      const material = new THREE.MeshBasicMaterial({
        color: new THREE.Color().setHSL(0.8 + i * 0.1, 0.9, 0.6),
        wireframe: true,
        transparent: true,
        opacity: 0.4
      })
      const torus = new THREE.Mesh(geometry, material)
      torus.position.set(
        (Math.random() - 0.5) * 8,
        (Math.random() - 0.5) * 8,
        (Math.random() - 0.5) * 8
      )
      shapes.push(torus)
      scene.add(torus)
    }

    // Animation loop
    const animate = () => {
      animationIdRef.current = requestAnimationFrame(animate)

      // Rotate particles
      particles.rotation.x += 0.001
      particles.rotation.y += 0.002

      // Animate shapes
      shapes.forEach((shape, index) => {
        shape.rotation.x += 0.01 + index * 0.001
        shape.rotation.y += 0.01 + index * 0.001
        shape.rotation.z += 0.005 + index * 0.001
        
        // Floating motion
        shape.position.y += Math.sin(Date.now() * 0.001 + index) * 0.001
      })

      // Camera gentle movement
      camera.position.x = Math.sin(Date.now() * 0.0005) * 0.5
      camera.position.y = Math.cos(Date.now() * 0.0003) * 0.3

      renderer.render(scene, camera)
    }

    animate()

    // Handle window resize
    const handleResize = () => {
      camera.aspect = window.innerWidth / window.innerHeight
      camera.updateProjectionMatrix()
      renderer.setSize(window.innerWidth, window.innerHeight)
    }

    window.addEventListener('resize', handleResize)

    // Cleanup function
    return () => {
      window.removeEventListener('resize', handleResize)
      if (animationIdRef.current) {
        cancelAnimationFrame(animationIdRef.current)
      }
      if (mountRef.current && renderer.domElement) {
        mountRef.current.removeChild(renderer.domElement)
      }
      renderer.dispose()
      
      // Dispose geometries and materials
      scene.traverse((object) => {
        if (object.geometry) object.geometry.dispose()
        if (object.material) {
          if (Array.isArray(object.material)) {
            object.material.forEach(material => material.dispose())
          } else {
            object.material.dispose()
          }
        }
      })
    }
  }, [])

  return (
    <div 
      ref={mountRef} 
      className="fixed inset-0 z-0"
      style={{ 
        background: 'linear-gradient(135deg, #000000 0%, #0a0a0a 50%, #000000 100%)'
      }}
    />
  )
}

export default ThreeBackground
