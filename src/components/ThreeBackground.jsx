import { useEffect, useRef } from 'react'
import * as THREE from 'three'

const ThreeBackground = () => {
  const mountRef = useRef(null)
  const sceneRef = useRef(null)
  const rendererRef = useRef(null)
  const animationIdRef = useRef(null)
  const mouseRef = useRef({ x: 0, y: 0 })
  const targetMouseRef = useRef({ x: 0, y: 0 })
  const particlesRef = useRef(null)
  const shapesRef = useRef([])
  const connectionLinesRef = useRef(null)

  useEffect(() => {
    if (!mountRef.current) return

    // Scene setup
    const scene = new THREE.Scene()
    scene.background = new THREE.Color(0x000000) // Pure black background
    sceneRef.current = scene

    // Camera setup
    const camera = new THREE.PerspectiveCamera(
      75,
      window.innerWidth / window.innerHeight,
      0.1,
      1000
    )
    camera.position.z = 5

    // Renderer setup with enhanced settings
    const renderer = new THREE.WebGLRenderer({
      antialias: true,
      alpha: true,
      powerPreference: "high-performance"
    })
    renderer.setSize(window.innerWidth, window.innerHeight)
    renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2))
    renderer.toneMapping = THREE.ACESFilmicToneMapping
    renderer.toneMappingExposure = 1.2
    renderer.outputColorSpace = THREE.SRGBColorSpace
    rendererRef.current = renderer
    mountRef.current.appendChild(renderer.domElement)

    // Add ambient lighting for better visual depth
    const ambientLight = new THREE.AmbientLight(0x404040, 0.3)
    scene.add(ambientLight)

    // Add point lights for dynamic lighting
    const pointLight1 = new THREE.PointLight(0x00ffff, 1, 100)
    pointLight1.position.set(10, 10, 10)
    scene.add(pointLight1)

    const pointLight2 = new THREE.PointLight(0xff00ff, 1, 100)
    pointLight2.position.set(-10, -10, 10)
    scene.add(pointLight2)

    // Create enhanced floating particles
    const particleGeometry = new THREE.BufferGeometry()
    const particleCount = 2000
    const positions = new Float32Array(particleCount * 3)
    const colors = new Float32Array(particleCount * 3)
    const sizes = new Float32Array(particleCount)
    const velocities = new Float32Array(particleCount * 3)

    for (let i = 0; i < particleCount * 3; i += 3) {
      // Random positions in a larger space
      positions[i] = (Math.random() - 0.5) * 30
      positions[i + 1] = (Math.random() - 0.5) * 30
      positions[i + 2] = (Math.random() - 0.5) * 30

      // Random velocities for floating motion
      velocities[i] = (Math.random() - 0.5) * 0.02
      velocities[i + 1] = (Math.random() - 0.5) * 0.02
      velocities[i + 2] = (Math.random() - 0.5) * 0.02

      // Enhanced color palette
      const colorChoice = Math.random()
      if (colorChoice < 0.2) {
        // Electric Blue
        colors[i] = 0.0
        colors[i + 1] = 0.5
        colors[i + 2] = 1.0
      } else if (colorChoice < 0.4) {
        // Cyan
        colors[i] = 0.0
        colors[i + 1] = 1.0
        colors[i + 2] = 1.0
      } else if (colorChoice < 0.6) {
        // Purple
        colors[i] = 0.7
        colors[i + 1] = 0.0
        colors[i + 2] = 1.0
      } else if (colorChoice < 0.8) {
        // Pink
        colors[i] = 1.0
        colors[i + 1] = 0.0
        colors[i + 2] = 0.7
      } else {
        // White/Silver
        colors[i] = 0.9
        colors[i + 1] = 0.9
        colors[i + 2] = 1.0
      }

      // Variable sizes
      sizes[i / 3] = Math.random() * 0.03 + 0.01
    }

    particleGeometry.setAttribute('position', new THREE.BufferAttribute(positions, 3))
    particleGeometry.setAttribute('color', new THREE.BufferAttribute(colors, 3))
    particleGeometry.setAttribute('size', new THREE.BufferAttribute(sizes, 1))

    const particleMaterial = new THREE.PointsMaterial({
      size: 0.03,
      vertexColors: true,
      transparent: true,
      opacity: 0.9,
      blending: THREE.AdditiveBlending,
      sizeAttenuation: true
    })

    const particles = new THREE.Points(particleGeometry, particleMaterial)
    particles.userData = { velocities } // Store velocities for animation
    particlesRef.current = particles
    scene.add(particles)

    // Create enhanced geometric shapes
    const shapes = []

    // Wireframe spheres with glow effect
    for (let i = 0; i < 8; i++) {
      const geometry = new THREE.SphereGeometry(0.3 + Math.random() * 0.4, 16, 16)
      const material = new THREE.MeshBasicMaterial({
        color: new THREE.Color().setHSL(0.5 + i * 0.08, 0.9, 0.6),
        wireframe: true,
        transparent: true,
        opacity: 0.4
      })
      const sphere = new THREE.Mesh(geometry, material)
      sphere.position.set(
        (Math.random() - 0.5) * 15,
        (Math.random() - 0.5) * 15,
        (Math.random() - 0.5) * 15
      )
      sphere.userData = {
        originalPosition: sphere.position.clone(),
        rotationSpeed: { x: Math.random() * 0.02, y: Math.random() * 0.02, z: Math.random() * 0.02 }
      }
      shapes.push(sphere)
      scene.add(sphere)
    }

    // Wireframe torus with varying sizes
    for (let i = 0; i < 5; i++) {
      const radius = 0.5 + Math.random() * 0.8
      const tube = 0.1 + Math.random() * 0.2
      const geometry = new THREE.TorusGeometry(radius, tube, 8, 16)
      const material = new THREE.MeshBasicMaterial({
        color: new THREE.Color().setHSL(0.7 + i * 0.06, 0.9, 0.7),
        wireframe: true,
        transparent: true,
        opacity: 0.5
      })
      const torus = new THREE.Mesh(geometry, material)
      torus.position.set(
        (Math.random() - 0.5) * 12,
        (Math.random() - 0.5) * 12,
        (Math.random() - 0.5) * 12
      )
      torus.userData = {
        originalPosition: torus.position.clone(),
        rotationSpeed: { x: Math.random() * 0.03, y: Math.random() * 0.03, z: Math.random() * 0.03 }
      }
      shapes.push(torus)
      scene.add(torus)
    }

    // Add some octahedrons for variety
    for (let i = 0; i < 4; i++) {
      const geometry = new THREE.OctahedronGeometry(0.4 + Math.random() * 0.3)
      const material = new THREE.MeshBasicMaterial({
        color: new THREE.Color().setHSL(0.9 + i * 0.05, 0.8, 0.6),
        wireframe: true,
        transparent: true,
        opacity: 0.6
      })
      const octahedron = new THREE.Mesh(geometry, material)
      octahedron.position.set(
        (Math.random() - 0.5) * 10,
        (Math.random() - 0.5) * 10,
        (Math.random() - 0.5) * 10
      )
      octahedron.userData = {
        originalPosition: octahedron.position.clone(),
        rotationSpeed: { x: Math.random() * 0.025, y: Math.random() * 0.025, z: Math.random() * 0.025 }
      }
      shapes.push(octahedron)
      scene.add(octahedron)
    }

    shapesRef.current = shapes

    // Create connection lines between nearby shapes
    const createConnectionLines = () => {
      const lineGeometry = new THREE.BufferGeometry()
      const linePositions = []
      const lineColors = []

      for (let i = 0; i < shapes.length; i++) {
        for (let j = i + 1; j < shapes.length; j++) {
          const distance = shapes[i].position.distanceTo(shapes[j].position)
          if (distance < 8) {
            // Add line between shapes
            linePositions.push(
              shapes[i].position.x, shapes[i].position.y, shapes[i].position.z,
              shapes[j].position.x, shapes[j].position.y, shapes[j].position.z
            )

            // Color based on distance (closer = brighter)
            const intensity = 1 - (distance / 8)
            lineColors.push(
              0.3 * intensity, 0.6 * intensity, 1.0 * intensity,
              0.3 * intensity, 0.6 * intensity, 1.0 * intensity
            )
          }
        }
      }

      lineGeometry.setAttribute('position', new THREE.Float32BufferAttribute(linePositions, 3))
      lineGeometry.setAttribute('color', new THREE.Float32BufferAttribute(lineColors, 3))

      const lineMaterial = new THREE.LineBasicMaterial({
        vertexColors: true,
        transparent: true,
        opacity: 0.3,
        blending: THREE.AdditiveBlending
      })

      const lines = new THREE.LineSegments(lineGeometry, lineMaterial)
      connectionLinesRef.current = lines
      scene.add(lines)
    }

    createConnectionLines()

    // Mouse interaction
    const handleMouseMove = (event) => {
      targetMouseRef.current.x = (event.clientX / window.innerWidth) * 2 - 1
      targetMouseRef.current.y = -(event.clientY / window.innerHeight) * 2 + 1
    }

    const handleMouseClick = (event) => {
      // Create ripple effect on click
      const rippleGeometry = new THREE.RingGeometry(0, 0.1, 16)
      const rippleMaterial = new THREE.MeshBasicMaterial({
        color: 0x00ffff,
        transparent: true,
        opacity: 0.8,
        side: THREE.DoubleSide
      })
      const ripple = new THREE.Mesh(rippleGeometry, rippleMaterial)

      // Position ripple at mouse position in 3D space
      const mouse = new THREE.Vector2(
        (event.clientX / window.innerWidth) * 2 - 1,
        -(event.clientY / window.innerHeight) * 2 + 1
      )
      const raycaster = new THREE.Raycaster()
      raycaster.setFromCamera(mouse, camera)
      const intersectPoint = raycaster.ray.at(5, new THREE.Vector3())

      ripple.position.copy(intersectPoint)
      ripple.lookAt(camera.position)
      scene.add(ripple)

      // Animate ripple
      let rippleScale = 0
      const animateRipple = () => {
        rippleScale += 0.1
        ripple.scale.setScalar(rippleScale)
        ripple.material.opacity = Math.max(0, 0.8 - rippleScale * 0.1)

        if (ripple.material.opacity > 0) {
          requestAnimationFrame(animateRipple)
        } else {
          scene.remove(ripple)
          ripple.geometry.dispose()
          ripple.material.dispose()
        }
      }
      animateRipple()
    }

    window.addEventListener('mousemove', handleMouseMove)
    window.addEventListener('click', handleMouseClick)

    // Enhanced animation loop
    const animate = () => {
      animationIdRef.current = requestAnimationFrame(animate)

      // Smooth mouse following
      mouseRef.current.x += (targetMouseRef.current.x - mouseRef.current.x) * 0.05
      mouseRef.current.y += (targetMouseRef.current.y - mouseRef.current.y) * 0.05

      // Animate particles with mouse influence
      const particlePositions = particles.geometry.attributes.position.array
      const particleVelocities = particles.userData.velocities
      for (let i = 0; i < particlePositions.length; i += 3) {
        // Add mouse influence
        const mouseInfluence = 0.02
        particlePositions[i] += particleVelocities[i] + mouseRef.current.x * mouseInfluence * 0.1
        particlePositions[i + 1] += particleVelocities[i + 1] + mouseRef.current.y * mouseInfluence * 0.1
        particlePositions[i + 2] += particleVelocities[i + 2]

        // Boundary wrapping
        if (Math.abs(particlePositions[i]) > 15) particlePositions[i] *= -0.8
        if (Math.abs(particlePositions[i + 1]) > 15) particlePositions[i + 1] *= -0.8
        if (Math.abs(particlePositions[i + 2]) > 15) particlePositions[i + 2] *= -0.8
      }
      particles.geometry.attributes.position.needsUpdate = true

      // Rotate particles
      particles.rotation.x += 0.001
      particles.rotation.y += 0.002

      // Animate shapes with mouse interaction
      shapes.forEach((shape, index) => {
        // Mouse attraction
        const mouseInfluence = 0.1
        const targetX = shape.userData.originalPosition.x + mouseRef.current.x * mouseInfluence
        const targetY = shape.userData.originalPosition.y + mouseRef.current.y * mouseInfluence

        shape.position.x += (targetX - shape.position.x) * 0.02
        shape.position.y += (targetY - shape.position.y) * 0.02

        // Enhanced rotation
        shape.rotation.x += shape.userData.rotationSpeed.x
        shape.rotation.y += shape.userData.rotationSpeed.y
        shape.rotation.z += shape.userData.rotationSpeed.z

        // Floating motion
        shape.position.z = shape.userData.originalPosition.z + Math.sin(Date.now() * 0.001 + index) * 0.5
      })

      // Animate point lights
      const time = Date.now() * 0.001
      pointLight1.position.x = Math.sin(time * 0.7) * 8
      pointLight1.position.y = Math.cos(time * 0.5) * 6
      pointLight1.position.z = Math.sin(time * 0.3) * 8

      pointLight2.position.x = Math.cos(time * 0.6) * 8
      pointLight2.position.y = Math.sin(time * 0.8) * 6
      pointLight2.position.z = Math.cos(time * 0.4) * 8

      // Dynamic camera movement with mouse influence
      camera.position.x = Math.sin(Date.now() * 0.0005) * 0.5 + mouseRef.current.x * 0.5
      camera.position.y = Math.cos(Date.now() * 0.0003) * 0.3 + mouseRef.current.y * 0.3
      camera.lookAt(mouseRef.current.x * 2, mouseRef.current.y * 2, 0)

      renderer.render(scene, camera)
    }

    animate()

    // Handle window resize
    const handleResize = () => {
      camera.aspect = window.innerWidth / window.innerHeight
      camera.updateProjectionMatrix()
      renderer.setSize(window.innerWidth, window.innerHeight)
    }

    window.addEventListener('resize', handleResize)

    // Cleanup function
    return () => {
      window.removeEventListener('resize', handleResize)
      window.removeEventListener('mousemove', handleMouseMove)
      window.removeEventListener('click', handleMouseClick)

      if (animationIdRef.current) {
        cancelAnimationFrame(animationIdRef.current)
      }
      if (mountRef.current && renderer.domElement) {
        mountRef.current.removeChild(renderer.domElement)
      }
      renderer.dispose()

      // Dispose geometries and materials
      scene.traverse((object) => {
        if (object.geometry) object.geometry.dispose()
        if (object.material) {
          if (Array.isArray(object.material)) {
            object.material.forEach(material => material.dispose())
          } else {
            object.material.dispose()
          }
        }
      })
    }
  }, [])

  return (
    <div
      ref={mountRef}
      className="fixed inset-0 z-0"
      style={{
        background: 'linear-gradient(135deg, #000000 0%, #0a0a0a 50%, #000000 100%)'
      }}
    />
  )
}

export default ThreeBackground
