import { useEffect, useRef } from 'react'

const CursorManager = () => {
  const lastMousePos = useRef({ x: 0, y: 0 })
  const currentAngle = useRef(0)
  const targetAngle = useRef(0)
  const animationId = useRef(null)

  useEffect(() => {
    let isMoving = false
    let moveTimeout = null

    // Create different cursor SVGs for different states
    const createCursorSVG = (angle, isActive = false) => {
      const baseColor = isActive ? '%23ffaa00' : '%23ffffff'
      const accentColor = isActive ? '%23ff6600' : '%2300ffff'
      const thrusterOpacity = isActive ? '1' : '0.6'
      
      return `data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='32' height='32' viewBox='0 0 32 32'%3E%3Cg transform='rotate(${angle} 16 16)'%3E%3Cg fill='${baseColor}' stroke='${accentColor}' stroke-width='1'%3E%3Cpath d='M16 2 L20 8 L18 10 L16 8 L14 10 L12 8 Z'/%3E%3Cpath d='M16 8 L18 12 L16 14 L14 12 Z'/%3E%3Cpath d='M12 8 L8 12 L10 14 L14 12 Z'/%3E%3Cpath d='M20 8 L24 12 L22 14 L18 12 Z'/%3E%3Cpath d='M14 12 L18 12 L20 16 L16 18 L12 16 Z'/%3E%3Cpath d='M10 14 L8 18 L12 16 Z'/%3E%3Cpath d='M22 14 L24 18 L20 16 Z'/%3E%3Cpath d='M12 16 L16 18 L20 16 L18 20 L16 22 L14 20 Z'/%3E%3C/g%3E%3Cg opacity='${thrusterOpacity}'%3E%3Ccircle cx='8' cy='8' r='1' fill='${accentColor}'%3E%3Canimate attributeName='opacity' values='0.8;0.3;0.8' dur='1.5s' repeatCount='indefinite'/%3E%3C/circle%3E%3Ccircle cx='24' cy='8' r='1' fill='${accentColor}'%3E%3Canimate attributeName='opacity' values='0.3;0.8;0.3' dur='1.5s' repeatCount='indefinite'/%3E%3C/circle%3E%3C/g%3E${isActive ? '%3Cg opacity="0.8"%3E%3Cpath d="M12 20 L14 26 L16 24 L18 26 L20 20" fill="%23ff3300" stroke="%23ff6600"%3E%3Canimate attributeName="opacity" values="1;0.5;1" dur="0.3s" repeatCount="indefinite"/%3E%3C/path%3E%3C/g%3E' : ''}%3C/g%3E%3C/svg%3E`
    }

    // Smooth angle interpolation
    const lerpAngle = (current, target, factor) => {
      let diff = target - current
      if (diff > 180) diff -= 360
      if (diff < -180) diff += 360
      return current + diff * factor
    }

    // Animation loop for smooth cursor rotation
    const animateCursor = () => {
      const angleDiff = Math.abs(targetAngle.current - currentAngle.current)
      if (angleDiff > 0.5) {
        currentAngle.current = lerpAngle(currentAngle.current, targetAngle.current, 0.15)
        
        // Update cursor with smooth rotation
        const cursorSVG = createCursorSVG(currentAngle.current, isMoving)
        document.body.style.cursor = `url("${cursorSVG}") 16 16, auto`
        
        animationId.current = requestAnimationFrame(animateCursor)
      }
    }

    const handleMouseMove = (event) => {
      const currentX = event.clientX
      const currentY = event.clientY
      
      const deltaX = currentX - lastMousePos.current.x
      const deltaY = currentY - lastMousePos.current.y
      
      // Only update direction if there's significant movement
      if (Math.abs(deltaX) > 2 || Math.abs(deltaY) > 2) {
        // Calculate angle (0 degrees = pointing right)
        const angle = Math.atan2(deltaY, deltaX) * (180 / Math.PI)
        targetAngle.current = angle
        
        // Set moving state
        isMoving = true
        clearTimeout(moveTimeout)
        
        // Start animation if not already running
        if (!animationId.current) {
          animateCursor()
        }
        
        // Reset moving state after movement stops
        moveTimeout = setTimeout(() => {
          isMoving = false
          // Update cursor to non-active state
          const cursorSVG = createCursorSVG(currentAngle.current, false)
          document.body.style.cursor = `url("${cursorSVG}") 16 16, auto`
        }, 150)
      }
      
      lastMousePos.current = { x: currentX, y: currentY }
    }

    // Handle mouse enter/leave for different elements
    const handleElementHover = (event) => {
      const element = event.target
      const isInteractive = element.matches('a, button, [role="button"], .cursor-pointer')
      
      if (isInteractive) {
        // Create enhanced cursor for interactive elements
        const enhancedCursorSVG = createCursorSVG(currentAngle.current, true)
        element.style.cursor = `url("${enhancedCursorSVG}") 16 16, pointer`
      }
    }

    const handleElementLeave = (event) => {
      const element = event.target
      const isInteractive = element.matches('a, button, [role="button"], .cursor-pointer')
      
      if (isInteractive) {
        // Reset to normal directional cursor
        const normalCursorSVG = createCursorSVG(currentAngle.current, false)
        element.style.cursor = `url("${normalCursorSVG}") 16 16, auto`
      }
    }

    // Initialize with default cursor
    const initialCursor = createCursorSVG(0, false)
    document.body.style.cursor = `url("${initialCursor}") 16 16, auto`

    // Add event listeners
    document.addEventListener('mousemove', handleMouseMove)
    document.addEventListener('mouseover', handleElementHover)
    document.addEventListener('mouseout', handleElementLeave)

    // Cleanup
    return () => {
      document.removeEventListener('mousemove', handleMouseMove)
      document.removeEventListener('mouseover', handleElementHover)
      document.removeEventListener('mouseout', handleElementLeave)
      
      if (animationId.current) {
        cancelAnimationFrame(animationId.current)
      }
      
      clearTimeout(moveTimeout)
      
      // Reset cursor
      document.body.style.cursor = 'auto'
    }
  }, [])

  return null // This component doesn't render anything
}

export default CursorManager
