import { useEffect, useRef } from 'react'
import * as THREE from 'three'

const Background3D = () => {
  const mountRef = useRef(null)
  const animationIdRef = useRef(null)
  const mouseRef = useRef({ x: 0, y: 0 })
  const targetMouseRef = useRef({ x: 0, y: 0 })

  useEffect(() => {
    if (!mountRef.current) return

    console.log('Background3D: Initializing...')

    // Scene setup
    const scene = new THREE.Scene()
    scene.background = new THREE.Color(0x000000)

    // Camera setup
    const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000)
    camera.position.z = 5

    // Renderer setup
    const renderer = new THREE.WebGLRenderer({
      antialias: true,
      alpha: true,
      preserveDrawingBuffer: false,
      powerPreference: "high-performance"
    })
    renderer.setSize(window.innerWidth, window.innerHeight, false)
    renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2))

    // Ensure canvas styling for proper display
    renderer.domElement.style.position = 'absolute'
    renderer.domElement.style.top = '0'
    renderer.domElement.style.left = '0'
    renderer.domElement.style.width = '100%'
    renderer.domElement.style.height = '100%'
    renderer.domElement.style.pointerEvents = 'auto'

    mountRef.current.appendChild(renderer.domElement)

    // Create particles
    const particleGeometry = new THREE.BufferGeometry()
    const particleCount = 1500
    const positions = new Float32Array(particleCount * 3)
    const colors = new Float32Array(particleCount * 3)
    const velocities = new Float32Array(particleCount * 3)

    for (let i = 0; i < particleCount * 3; i += 3) {
      positions[i] = (Math.random() - 0.5) * 20
      positions[i + 1] = (Math.random() - 0.5) * 20
      positions[i + 2] = (Math.random() - 0.5) * 20

      velocities[i] = (Math.random() - 0.5) * 0.02
      velocities[i + 1] = (Math.random() - 0.5) * 0.02
      velocities[i + 2] = (Math.random() - 0.5) * 0.02

      const colorChoice = Math.random()
      if (colorChoice < 0.25) {
        colors[i] = 0.0; colors[i + 1] = 0.5; colors[i + 2] = 1.0 // Blue
      } else if (colorChoice < 0.5) {
        colors[i] = 0.0; colors[i + 1] = 1.0; colors[i + 2] = 1.0 // Cyan
      } else if (colorChoice < 0.75) {
        colors[i] = 0.7; colors[i + 1] = 0.0; colors[i + 2] = 1.0 // Purple
      } else {
        colors[i] = 1.0; colors[i + 1] = 0.0; colors[i + 2] = 0.7 // Pink
      }
    }

    particleGeometry.setAttribute('position', new THREE.BufferAttribute(positions, 3))
    particleGeometry.setAttribute('color', new THREE.BufferAttribute(colors, 3))

    const particleMaterial = new THREE.PointsMaterial({
      size: 0.03,
      vertexColors: true,
      transparent: true,
      opacity: 0.8,
      blending: THREE.AdditiveBlending
    })

    const particles = new THREE.Points(particleGeometry, particleMaterial)
    scene.add(particles)

    // Create geometric shapes
    const shapes = []

    // Spheres
    for (let i = 0; i < 6; i++) {
      const geometry = new THREE.SphereGeometry(0.3 + Math.random() * 0.3, 16, 16)
      const material = new THREE.MeshBasicMaterial({
        color: new THREE.Color().setHSL(0.5 + i * 0.1, 0.8, 0.6),
        wireframe: true,
        transparent: true,
        opacity: 0.4
      })
      const sphere = new THREE.Mesh(geometry, material)
      sphere.position.set(
        (Math.random() - 0.5) * 10,
        (Math.random() - 0.5) * 10,
        (Math.random() - 0.5) * 10
      )
      shapes.push(sphere)
      scene.add(sphere)
    }

    // Torus
    for (let i = 0; i < 4; i++) {
      const geometry = new THREE.TorusGeometry(0.5 + Math.random() * 0.5, 0.1 + Math.random() * 0.1, 8, 16)
      const material = new THREE.MeshBasicMaterial({
        color: new THREE.Color().setHSL(0.7 + i * 0.08, 0.9, 0.7),
        wireframe: true,
        transparent: true,
        opacity: 0.5
      })
      const torus = new THREE.Mesh(geometry, material)
      torus.position.set(
        (Math.random() - 0.5) * 8,
        (Math.random() - 0.5) * 8,
        (Math.random() - 0.5) * 8
      )
      shapes.push(torus)
      scene.add(torus)
    }

    // Mouse interaction
    const handleMouseMove = (event) => {
      targetMouseRef.current.x = (event.clientX / window.innerWidth) * 2 - 1
      targetMouseRef.current.y = -(event.clientY / window.innerHeight) * 2 + 1
    }

    const handleClick = (event) => {
      // Create ripple effect
      const rippleGeometry = new THREE.RingGeometry(0, 0.1, 16)
      const rippleMaterial = new THREE.MeshBasicMaterial({
        color: 0x00ffff,
        transparent: true,
        opacity: 0.8,
        side: THREE.DoubleSide
      })
      const ripple = new THREE.Mesh(rippleGeometry, rippleMaterial)

      const mouse = new THREE.Vector2(
        (event.clientX / window.innerWidth) * 2 - 1,
        -(event.clientY / window.innerHeight) * 2 + 1
      )
      const raycaster = new THREE.Raycaster()
      raycaster.setFromCamera(mouse, camera)
      const intersectPoint = raycaster.ray.at(5, new THREE.Vector3())

      ripple.position.copy(intersectPoint)
      ripple.lookAt(camera.position)
      scene.add(ripple)

      let rippleScale = 0
      const animateRipple = () => {
        rippleScale += 0.1
        ripple.scale.setScalar(rippleScale)
        ripple.material.opacity = Math.max(0, 0.8 - rippleScale * 0.1)

        if (ripple.material.opacity > 0) {
          requestAnimationFrame(animateRipple)
        } else {
          scene.remove(ripple)
          ripple.geometry.dispose()
          ripple.material.dispose()
        }
      }
      animateRipple()
    }

    window.addEventListener('mousemove', handleMouseMove)
    window.addEventListener('click', handleClick)

    // Animation loop with better error handling
    const animate = () => {
      if (!mountRef.current || !renderer || !scene || !camera) return

      animationIdRef.current = requestAnimationFrame(animate)

      // Smooth mouse following
      mouseRef.current.x += (targetMouseRef.current.x - mouseRef.current.x) * 0.05
      mouseRef.current.y += (targetMouseRef.current.y - mouseRef.current.y) * 0.05

      // Animate particles
      const particlePositions = particles.geometry.attributes.position.array
      for (let i = 0; i < particlePositions.length; i += 3) {
        particlePositions[i] += velocities[i] + mouseRef.current.x * 0.002
        particlePositions[i + 1] += velocities[i + 1] + mouseRef.current.y * 0.002
        particlePositions[i + 2] += velocities[i + 2]

        // Boundary wrapping
        if (Math.abs(particlePositions[i]) > 10) particlePositions[i] *= -0.8
        if (Math.abs(particlePositions[i + 1]) > 10) particlePositions[i + 1] *= -0.8
        if (Math.abs(particlePositions[i + 2]) > 10) particlePositions[i + 2] *= -0.8
      }
      particles.geometry.attributes.position.needsUpdate = true

      // Rotate particles
      particles.rotation.x += 0.001
      particles.rotation.y += 0.002

      // Animate shapes
      shapes.forEach((shape, index) => {
        shape.rotation.x += 0.01 + index * 0.001
        shape.rotation.y += 0.01 + index * 0.001
        shape.rotation.z += 0.005 + index * 0.001

        // Mouse attraction
        shape.position.x += (mouseRef.current.x * 2 - shape.position.x) * 0.01
        shape.position.y += (mouseRef.current.y * 2 - shape.position.y) * 0.01

        // Floating motion
        shape.position.z += Math.sin(Date.now() * 0.001 + index) * 0.001
      })

      // Camera movement
      camera.position.x = Math.sin(Date.now() * 0.0005) * 0.5 + mouseRef.current.x * 0.3
      camera.position.y = Math.cos(Date.now() * 0.0003) * 0.3 + mouseRef.current.y * 0.3
      camera.lookAt(mouseRef.current.x, mouseRef.current.y, 0)

      renderer.render(scene, camera)
    }

    animate()

    // Enhanced resize and display change handling with debouncing
    let resizeTimeout
    const handleResize = () => {
      clearTimeout(resizeTimeout)
      resizeTimeout = setTimeout(() => {
        const width = window.innerWidth
        const height = window.innerHeight
        const pixelRatio = Math.min(window.devicePixelRatio, 2)

        camera.aspect = width / height
        camera.updateProjectionMatrix()

        renderer.setSize(width, height, false)
        renderer.setPixelRatio(pixelRatio)

        // Force a render to prevent glitches
        renderer.render(scene, camera)

        console.log('Background3D: Resized to', width, 'x', height, 'with pixel ratio', pixelRatio)
      }, 100)
    }

    // Handle display changes (moving between monitors)
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        // Page became visible again, force resize
        setTimeout(handleResize, 100)
      }
    }

    // Handle focus changes
    const handleFocus = () => {
      setTimeout(handleResize, 100)
    }

    window.addEventListener('resize', handleResize)
    window.addEventListener('orientationchange', handleResize)
    document.addEventListener('visibilitychange', handleVisibilityChange)
    window.addEventListener('focus', handleFocus)

    console.log('Background3D: Setup complete')

    // Cleanup
    return () => {
      console.log('Background3D: Cleaning up...')
      window.removeEventListener('resize', handleResize)
      window.removeEventListener('orientationchange', handleResize)
      document.removeEventListener('visibilitychange', handleVisibilityChange)
      window.removeEventListener('focus', handleFocus)
      window.removeEventListener('mousemove', handleMouseMove)
      window.removeEventListener('click', handleClick)

      if (animationIdRef.current) {
        cancelAnimationFrame(animationIdRef.current)
      }
      if (mountRef.current && renderer.domElement) {
        mountRef.current.removeChild(renderer.domElement)
      }
      renderer.dispose()

      scene.traverse((object) => {
        if (object.geometry) object.geometry.dispose()
        if (object.material) {
          if (Array.isArray(object.material)) {
            object.material.forEach(material => material.dispose())
          } else {
            object.material.dispose()
          }
        }
      })
    }
  }, [])

  return (
    <div
      ref={mountRef}
      className="fixed inset-0 z-0"
      style={{
        background: 'linear-gradient(135deg, #000000 0%, #0a0a0a 50%, #000000 100%)'
      }}
    />
  )
}

export default Background3D
