import { useEffect, useRef } from 'react'
import * as THREE from 'three'

const Background3D = () => {
  const mountRef = useRef(null)
  const animationIdRef = useRef(null)
  const mouseRef = useRef({ x: 0, y: 0 })
  const targetMouseRef = useRef({ x: 0, y: 0 })

  useEffect(() => {
    if (!mountRef.current) return

    console.log('Background3D: Initializing...')

    // Scene setup
    const scene = new THREE.Scene()
    scene.background = new THREE.Color(0x000000)

    // Camera setup
    const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000)
    camera.position.z = 5

    // Renderer setup
    const renderer = new THREE.WebGLRenderer({
      antialias: true,
      alpha: true,
      preserveDrawingBuffer: false,
      powerPreference: "high-performance"
    })
    renderer.setSize(window.innerWidth, window.innerHeight, false)
    renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2))

    // Ensure canvas styling for proper display
    renderer.domElement.style.position = 'absolute'
    renderer.domElement.style.top = '0'
    renderer.domElement.style.left = '0'
    renderer.domElement.style.width = '100%'
    renderer.domElement.style.height = '100%'
    renderer.domElement.style.pointerEvents = 'auto'

    mountRef.current.appendChild(renderer.domElement)

    // Create space stars (distant background)
    const createStars = () => {
      const starGeometry = new THREE.BufferGeometry()
      const starCount = 2000
      const starPositions = new Float32Array(starCount * 3)
      const starColors = new Float32Array(starCount * 3)
      const starSizes = new Float32Array(starCount)

      for (let i = 0; i < starCount * 3; i += 3) {
        // Distribute stars in a large sphere
        const radius = 50 + Math.random() * 100
        const theta = Math.random() * Math.PI * 2
        const phi = Math.acos(2 * Math.random() - 1)

        starPositions[i] = radius * Math.sin(phi) * Math.cos(theta)
        starPositions[i + 1] = radius * Math.sin(phi) * Math.sin(theta)
        starPositions[i + 2] = radius * Math.cos(phi)

        // Star colors - white, blue-white, yellow-white
        const starType = Math.random()
        if (starType < 0.6) {
          // White stars
          starColors[i] = 1.0; starColors[i + 1] = 1.0; starColors[i + 2] = 1.0
        } else if (starType < 0.8) {
          // Blue-white stars
          starColors[i] = 0.8; starColors[i + 1] = 0.9; starColors[i + 2] = 1.0
        } else {
          // Yellow-white stars
          starColors[i] = 1.0; starColors[i + 1] = 0.9; starColors[i + 2] = 0.7
        }

        starSizes[i / 3] = Math.random() * 0.02 + 0.005
      }

      starGeometry.setAttribute('position', new THREE.BufferAttribute(starPositions, 3))
      starGeometry.setAttribute('color', new THREE.BufferAttribute(starColors, 3))
      starGeometry.setAttribute('size', new THREE.BufferAttribute(starSizes, 1))

      const starMaterial = new THREE.PointsMaterial({
        size: 0.01,
        vertexColors: true,
        transparent: true,
        opacity: 0.9,
        blending: THREE.AdditiveBlending,
        sizeAttenuation: true
      })

      return new THREE.Points(starGeometry, starMaterial)
    }

    // Create floating cosmic dust/nebula particles
    const createCosmicDust = () => {
      const dustGeometry = new THREE.BufferGeometry()
      const dustCount = 800
      const dustPositions = new Float32Array(dustCount * 3)
      const dustColors = new Float32Array(dustCount * 3)
      const dustVelocities = new Float32Array(dustCount * 3)

      for (let i = 0; i < dustCount * 3; i += 3) {
        dustPositions[i] = (Math.random() - 0.5) * 30
        dustPositions[i + 1] = (Math.random() - 0.5) * 30
        dustPositions[i + 2] = (Math.random() - 0.5) * 30

        // Slower cosmic drift
        dustVelocities[i] = (Math.random() - 0.5) * 0.005
        dustVelocities[i + 1] = (Math.random() - 0.5) * 0.005
        dustVelocities[i + 2] = (Math.random() - 0.5) * 0.005

        // Nebula colors - purple, blue, pink cosmic dust
        const dustType = Math.random()
        if (dustType < 0.3) {
          // Purple nebula
          dustColors[i] = 0.6; dustColors[i + 1] = 0.2; dustColors[i + 2] = 0.8
        } else if (dustType < 0.6) {
          // Blue nebula
          dustColors[i] = 0.2; dustColors[i + 1] = 0.4; dustColors[i + 2] = 0.9
        } else {
          // Pink/magenta nebula
          dustColors[i] = 0.8; dustColors[i + 1] = 0.3; dustColors[i + 2] = 0.6
        }
      }

      dustGeometry.setAttribute('position', new THREE.BufferAttribute(dustPositions, 3))
      dustGeometry.setAttribute('color', new THREE.BufferAttribute(dustColors, 3))

      const dustMaterial = new THREE.PointsMaterial({
        size: 0.04,
        vertexColors: true,
        transparent: true,
        opacity: 0.6,
        blending: THREE.AdditiveBlending
      })

      const dust = new THREE.Points(dustGeometry, dustMaterial)
      dust.userData = { velocities: dustVelocities }
      return dust
    }

    const stars = createStars()
    const cosmicDust = createCosmicDust()
    scene.add(stars)
    scene.add(cosmicDust)

    // Create space objects (planets, asteroids, space stations)
    const spaceObjects = []

    // Planets (spheres with cosmic colors)
    for (let i = 0; i < 4; i++) {
      const geometry = new THREE.SphereGeometry(0.4 + Math.random() * 0.4, 20, 20)
      const material = new THREE.MeshBasicMaterial({
        color: new THREE.Color().setHSL(0.6 + i * 0.15, 0.7, 0.5),
        wireframe: true,
        transparent: true,
        opacity: 0.3
      })
      const planet = new THREE.Mesh(geometry, material)
      planet.position.set(
        (Math.random() - 0.5) * 15,
        (Math.random() - 0.5) * 15,
        (Math.random() - 0.5) * 15
      )
      planet.userData = {
        type: 'planet',
        rotationSpeed: { x: Math.random() * 0.003, y: Math.random() * 0.003, z: Math.random() * 0.003 },
        orbitSpeed: Math.random() * 0.001 + 0.0005
      }
      spaceObjects.push(planet)
      scene.add(planet)
    }

    // Space rings/portals (torus)
    for (let i = 0; i < 3; i++) {
      const geometry = new THREE.TorusGeometry(0.8 + Math.random() * 0.6, 0.05 + Math.random() * 0.05, 8, 24)
      const material = new THREE.MeshBasicMaterial({
        color: new THREE.Color().setHSL(0.8 + i * 0.1, 0.9, 0.6),
        wireframe: true,
        transparent: true,
        opacity: 0.4
      })
      const ring = new THREE.Mesh(geometry, material)
      ring.position.set(
        (Math.random() - 0.5) * 12,
        (Math.random() - 0.5) * 12,
        (Math.random() - 0.5) * 12
      )
      ring.userData = {
        type: 'ring',
        rotationSpeed: { x: Math.random() * 0.004, y: Math.random() * 0.004, z: Math.random() * 0.004 },
        orbitSpeed: Math.random() * 0.0008 + 0.0003
      }
      spaceObjects.push(ring)
      scene.add(ring)
    }

    // Asteroids (irregular shapes)
    for (let i = 0; i < 3; i++) {
      const geometry = new THREE.OctahedronGeometry(0.2 + Math.random() * 0.3)
      const material = new THREE.MeshBasicMaterial({
        color: new THREE.Color().setHSL(0.1 + i * 0.05, 0.6, 0.4),
        wireframe: true,
        transparent: true,
        opacity: 0.5
      })
      const asteroid = new THREE.Mesh(geometry, material)
      asteroid.position.set(
        (Math.random() - 0.5) * 10,
        (Math.random() - 0.5) * 10,
        (Math.random() - 0.5) * 10
      )
      asteroid.userData = {
        type: 'asteroid',
        rotationSpeed: { x: Math.random() * 0.006, y: Math.random() * 0.006, z: Math.random() * 0.006 },
        orbitSpeed: Math.random() * 0.0012 + 0.0008
      }
      spaceObjects.push(asteroid)
      scene.add(asteroid)
    }

    // Mouse interaction
    const handleMouseMove = (event) => {
      targetMouseRef.current.x = (event.clientX / window.innerWidth) * 2 - 1
      targetMouseRef.current.y = -(event.clientY / window.innerHeight) * 2 + 1
    }

    const handleClick = (event) => {
      // Create cosmic energy burst effect
      const burstGeometry = new THREE.RingGeometry(0, 0.1, 24)
      const burstMaterial = new THREE.MeshBasicMaterial({
        color: 0x9966ff, // Purple cosmic energy
        transparent: true,
        opacity: 0.9,
        side: THREE.DoubleSide
      })
      const burst = new THREE.Mesh(burstGeometry, burstMaterial)

      // Create secondary ring for layered effect
      const secondaryGeometry = new THREE.RingGeometry(0, 0.05, 16)
      const secondaryMaterial = new THREE.MeshBasicMaterial({
        color: 0x66ccff, // Blue energy
        transparent: true,
        opacity: 0.7,
        side: THREE.DoubleSide
      })
      const secondaryBurst = new THREE.Mesh(secondaryGeometry, secondaryMaterial)

      const mouse = new THREE.Vector2(
        (event.clientX / window.innerWidth) * 2 - 1,
        -(event.clientY / window.innerHeight) * 2 + 1
      )
      const raycaster = new THREE.Raycaster()
      raycaster.setFromCamera(mouse, camera)
      const intersectPoint = raycaster.ray.at(5, new THREE.Vector3())

      burst.position.copy(intersectPoint)
      burst.lookAt(camera.position)
      secondaryBurst.position.copy(intersectPoint)
      secondaryBurst.lookAt(camera.position)

      scene.add(burst)
      scene.add(secondaryBurst)

      let burstScale = 0
      const animateBurst = () => {
        burstScale += 0.05 // Slower expansion
        burst.scale.setScalar(burstScale)
        secondaryBurst.scale.setScalar(burstScale * 1.5)

        burst.material.opacity = Math.max(0, 0.9 - burstScale * 0.08)
        secondaryBurst.material.opacity = Math.max(0, 0.7 - burstScale * 0.1)

        // Add rotation for cosmic effect
        burst.rotation.z += 0.02
        secondaryBurst.rotation.z -= 0.03

        if (burst.material.opacity > 0) {
          requestAnimationFrame(animateBurst)
        } else {
          scene.remove(burst)
          scene.remove(secondaryBurst)
          burst.geometry.dispose()
          burst.material.dispose()
          secondaryBurst.geometry.dispose()
          secondaryBurst.material.dispose()
        }
      }
      animateBurst()
    }

    window.addEventListener('mousemove', handleMouseMove)
    window.addEventListener('click', handleClick)

    // Animation loop with better error handling
    const animate = () => {
      if (!mountRef.current || !renderer || !scene || !camera) return

      animationIdRef.current = requestAnimationFrame(animate)

      // Smooth mouse following (slower)
      mouseRef.current.x += (targetMouseRef.current.x - mouseRef.current.x) * 0.02
      mouseRef.current.y += (targetMouseRef.current.y - mouseRef.current.y) * 0.02

      const time = Date.now() * 0.0001 // Much slower time scale

      // Animate cosmic dust
      const dustPositions = cosmicDust.geometry.attributes.position.array
      const dustVelocities = cosmicDust.userData.velocities
      for (let i = 0; i < dustPositions.length; i += 3) {
        dustPositions[i] += dustVelocities[i] + mouseRef.current.x * 0.0005
        dustPositions[i + 1] += dustVelocities[i + 1] + mouseRef.current.y * 0.0005
        dustPositions[i + 2] += dustVelocities[i + 2]

        // Gentle boundary wrapping for cosmic effect
        if (Math.abs(dustPositions[i]) > 15) dustPositions[i] *= -0.9
        if (Math.abs(dustPositions[i + 1]) > 15) dustPositions[i + 1] *= -0.9
        if (Math.abs(dustPositions[i + 2]) > 15) dustPositions[i + 2] *= -0.9
      }
      cosmicDust.geometry.attributes.position.needsUpdate = true

      // Slow rotation for stars (distant background)
      stars.rotation.x += 0.0002
      stars.rotation.y += 0.0001

      // Gentle cosmic dust rotation
      cosmicDust.rotation.x += 0.0003
      cosmicDust.rotation.y += 0.0005

      // Animate space objects with orbital motion
      spaceObjects.forEach((object, index) => {
        const userData = object.userData

        // Individual rotation
        object.rotation.x += userData.rotationSpeed.x
        object.rotation.y += userData.rotationSpeed.y
        object.rotation.z += userData.rotationSpeed.z

        // Orbital motion around center
        const orbitRadius = 8 + index * 2
        const orbitAngle = time * userData.orbitSpeed + index * Math.PI * 0.5

        object.position.x = Math.cos(orbitAngle) * orbitRadius + mouseRef.current.x * 0.5
        object.position.y = Math.sin(orbitAngle) * orbitRadius * 0.7 + mouseRef.current.y * 0.5
        object.position.z = Math.sin(orbitAngle * 0.5) * 3
      })

      // Gentle camera drift through space
      camera.position.x = Math.sin(time * 0.3) * 0.3 + mouseRef.current.x * 0.2
      camera.position.y = Math.cos(time * 0.2) * 0.2 + mouseRef.current.y * 0.2
      camera.position.z = 5 + Math.sin(time * 0.1) * 0.5
      camera.lookAt(mouseRef.current.x * 0.5, mouseRef.current.y * 0.5, 0)

      renderer.render(scene, camera)
    }

    animate()

    // Enhanced resize and display change handling with debouncing
    let resizeTimeout
    const handleResize = () => {
      clearTimeout(resizeTimeout)
      resizeTimeout = setTimeout(() => {
        const width = window.innerWidth
        const height = window.innerHeight
        const pixelRatio = Math.min(window.devicePixelRatio, 2)

        camera.aspect = width / height
        camera.updateProjectionMatrix()

        renderer.setSize(width, height, false)
        renderer.setPixelRatio(pixelRatio)

        // Force a render to prevent glitches
        renderer.render(scene, camera)

        console.log('Background3D: Resized to', width, 'x', height, 'with pixel ratio', pixelRatio)
      }, 100)
    }

    // Handle display changes (moving between monitors)
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        // Page became visible again, force resize
        setTimeout(handleResize, 100)
      }
    }

    // Handle focus changes
    const handleFocus = () => {
      setTimeout(handleResize, 100)
    }

    window.addEventListener('resize', handleResize)
    window.addEventListener('orientationchange', handleResize)
    document.addEventListener('visibilitychange', handleVisibilityChange)
    window.addEventListener('focus', handleFocus)

    console.log('Background3D: Setup complete')

    // Cleanup
    return () => {
      console.log('Background3D: Cleaning up...')
      window.removeEventListener('resize', handleResize)
      window.removeEventListener('orientationchange', handleResize)
      document.removeEventListener('visibilitychange', handleVisibilityChange)
      window.removeEventListener('focus', handleFocus)
      window.removeEventListener('mousemove', handleMouseMove)
      window.removeEventListener('click', handleClick)

      if (animationIdRef.current) {
        cancelAnimationFrame(animationIdRef.current)
      }
      if (mountRef.current && renderer.domElement) {
        mountRef.current.removeChild(renderer.domElement)
      }
      renderer.dispose()

      scene.traverse((object) => {
        if (object.geometry) object.geometry.dispose()
        if (object.material) {
          if (Array.isArray(object.material)) {
            object.material.forEach(material => material.dispose())
          } else {
            object.material.dispose()
          }
        }
      })
    }
  }, [])

  return (
    <div
      ref={mountRef}
      className="fixed inset-0 z-0"
      style={{
        background: 'linear-gradient(135deg, #000000 0%, #0a0a0a 50%, #000000 100%)'
      }}
    />
  )
}

export default Background3D
