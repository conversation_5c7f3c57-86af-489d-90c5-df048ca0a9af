@import "tailwindcss";

:root {
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Custom Space Shuttle Cursors */
* {
  cursor: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='32' height='32' viewBox='0 0 32 32'%3E%3Cg fill='%23ffffff' stroke='%2300ffff' stroke-width='1'%3E%3Cpath d='M16 2 L20 8 L18 10 L16 8 L14 10 L12 8 Z'/%3E%3Cpath d='M16 8 L18 12 L16 14 L14 12 Z'/%3E%3Cpath d='M12 8 L8 12 L10 14 L14 12 Z'/%3E%3Cpath d='M20 8 L24 12 L22 14 L18 12 Z'/%3E%3Cpath d='M14 12 L18 12 L20 16 L16 18 L12 16 Z'/%3E%3Cpath d='M10 14 L8 18 L12 16 Z'/%3E%3Cpath d='M22 14 L24 18 L20 16 Z'/%3E%3Cpath d='M12 16 L16 18 L20 16 L18 20 L16 22 L14 20 Z'/%3E%3C/g%3E%3C/svg%3E") 16 16, auto;
}

/* Hover cursor for interactive elements */
a,
button,
[role="button"],
.cursor-pointer {
  cursor: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='32' height='32' viewBox='0 0 32 32'%3E%3Cg fill='%23ffaa00' stroke='%23ff6600' stroke-width='1.5'%3E%3Cpath d='M16 2 L22 8 L20 10 L16 6 L12 10 L10 8 Z'/%3E%3Cpath d='M16 6 L20 12 L16 16 L12 12 Z'/%3E%3Cpath d='M10 8 L6 12 L8 16 L12 12 Z'/%3E%3Cpath d='M22 8 L26 12 L24 16 L20 12 Z'/%3E%3Cpath d='M12 12 L20 12 L22 18 L16 20 L10 18 Z'/%3E%3Cpath d='M8 16 L6 20 L10 18 Z'/%3E%3Cpath d='M24 16 L26 20 L22 18 Z'/%3E%3Cpath d='M10 18 L16 20 L22 18 L20 24 L16 26 L12 24 Z'/%3E%3Cpath d='M14 22 L18 22 L16 28 Z'/%3E%3C/g%3E%3C/svg%3E") 16 16, pointer;
}

/* Loading/waiting cursor */
.cursor-wait {
  cursor: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='32' height='32' viewBox='0 0 32 32'%3E%3Cg fill='%2366ccff' stroke='%230099ff' stroke-width='1'%3E%3Cpath d='M16 4 L20 6 L18 8 L16 6 L14 8 L12 6 Z'/%3E%3Cpath d='M16 6 L18 10 L16 12 L14 10 Z'/%3E%3Cpath d='M12 6 L8 10 L10 12 L14 10 Z'/%3E%3Cpath d='M20 6 L24 10 L22 12 L18 10 Z'/%3E%3Cpath d='M14 10 L18 10 L20 14 L16 16 L12 14 Z'/%3E%3Cpath d='M10 12 L8 16 L12 14 Z'/%3E%3Cpath d='M22 12 L24 16 L20 14 Z'/%3E%3Cpath d='M12 14 L16 16 L20 14 L18 18 L16 20 L14 18 Z'/%3E%3Ccircle cx='16' cy='24' r='2' fill='%2300ffff'/%3E%3Ccircle cx='12' cy='22' r='1' fill='%2366ccff'/%3E%3Ccircle cx='20' cy='22' r='1' fill='%2366ccff'/%3E%3C/g%3E%3C/svg%3E") 16 16, wait;
}

/* Text selection cursor */
.cursor-text,
input,
textarea {
  cursor: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='32' height='32' viewBox='0 0 32 32'%3E%3Cg fill='%23ffffff' stroke='%2300ffff' stroke-width='1'%3E%3Cpath d='M16 2 L18 6 L16 8 L14 6 Z'/%3E%3Cpath d='M16 8 L17 12 L16 14 L15 12 Z'/%3E%3Cpath d='M15 12 L17 12 L18 16 L16 18 L14 16 Z'/%3E%3Cpath d='M14 16 L16 18 L18 16 L17 20 L16 22 L15 20 Z'/%3E%3Cpath d='M15 20 L17 20 L16 24 L14 22 L18 22 Z'/%3E%3Cline x1='12' y1='8' x2='20' y2='8' stroke='%2300ffff' stroke-width='2'/%3E%3Cline x1='12' y1='20' x2='20' y2='20' stroke='%2300ffff' stroke-width='2'/%3E%3C/g%3E%3C/svg%3E") 16 16, text;
}

/* Special cursor for Three.js background interaction */
.space-cursor {
  cursor: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='32' height='32' viewBox='0 0 32 32'%3E%3Cg fill='%23ffffff' stroke='%2300ffff' stroke-width='1'%3E%3Cpath d='M16 2 L20 8 L18 10 L16 8 L14 10 L12 8 Z'/%3E%3Cpath d='M16 8 L18 12 L16 14 L14 12 Z'/%3E%3Cpath d='M12 8 L8 12 L10 14 L14 12 Z'/%3E%3Cpath d='M20 8 L24 12 L22 14 L18 12 Z'/%3E%3Cpath d='M14 12 L18 12 L20 16 L16 18 L12 16 Z'/%3E%3Cpath d='M10 14 L8 18 L12 16 Z'/%3E%3Cpath d='M22 14 L24 18 L20 16 Z'/%3E%3Cpath d='M12 16 L16 18 L20 16 L18 20 L16 22 L14 20 Z'/%3E%3Ccircle cx='8' cy='8' r='1' fill='%2300ffff' opacity='0.8'%3E%3Canimate attributeName='opacity' values='0.8;0.3;0.8' dur='2s' repeatCount='indefinite'/%3E%3C/circle%3E%3Ccircle cx='24' cy='8' r='1' fill='%2300ffff' opacity='0.8'%3E%3Canimate attributeName='opacity' values='0.3;0.8;0.3' dur='2s' repeatCount='indefinite'/%3E%3C/circle%3E%3C/g%3E%3C/svg%3E") 16 16, auto;
}

/* Rocket boost cursor for active interactions */
.rocket-boost {
  cursor: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='32' height='32' viewBox='0 0 32 32'%3E%3Cg fill='%23ffaa00' stroke='%23ff6600' stroke-width='1.5'%3E%3Cpath d='M16 2 L22 8 L20 10 L16 6 L12 10 L10 8 Z'/%3E%3Cpath d='M16 6 L20 12 L16 16 L12 12 Z'/%3E%3Cpath d='M10 8 L6 12 L8 16 L12 12 Z'/%3E%3Cpath d='M22 8 L26 12 L24 16 L20 12 Z'/%3E%3Cpath d='M12 12 L20 12 L22 18 L16 20 L10 18 Z'/%3E%3Cpath d='M8 16 L6 20 L10 18 Z'/%3E%3Cpath d='M24 16 L26 20 L22 18 Z'/%3E%3Cpath d='M10 18 L16 20 L22 18 L20 24 L16 26 L12 24 Z'/%3E%3Cpath d='M14 22 L18 22 L16 28 Z'/%3E%3Cpath d='M12 24 L14 28 L16 26 L18 28 L20 24' fill='%23ff3300' stroke='%23ff6600'%3E%3Canimate attributeName='opacity' values='1;0.5;1' dur='0.5s' repeatCount='indefinite'/%3E%3C/path%3E%3C/g%3E%3C/svg%3E") 16 16, pointer;
}

/* Cursor animations */
@keyframes cursor-glow {

  0%,
  100% {
    filter: drop-shadow(0 0 2px #00ffff);
  }

  50% {
    filter: drop-shadow(0 0 8px #00ffff);
  }
}

/* Apply glow effect to interactive elements */
a:hover,
button:hover,
[role="button"]:hover {
  animation: cursor-glow 2s ease-in-out infinite;
}

a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
}

a:hover {
  color: #535bf2;
}

body {
  margin: 0;
  padding: 0;
  width: 100%;
  min-width: 320px;
  min-height: 100vh;
}

#root {
  width: 100%;
  min-height: 100vh;
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

button {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: #1a1a1a;
  cursor: pointer;
  transition: border-color 0.25s;
}

button:hover {
  border-color: #646cff;
}

button:focus,
button:focus-visible {
  outline: 4px auto -webkit-focus-ring-color;
}

@media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: #ffffff;
  }

  a:hover {
    color: #747bff;
  }

  button {
    background-color: #f9f9f9;
  }
}